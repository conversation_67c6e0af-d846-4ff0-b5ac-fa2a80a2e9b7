import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Layout, Card, Space, Typography, Button, message, Tag, Spin } from 'antd';
import {
  DownloadOutlined,
  SearchOutlined
} from '@ant-design/icons';
import { DevToolsControl } from './DevToolsControl';
import { FilterPanel } from './FilterPanel';
import { ResourceList } from './ResourceList';
import {
  CourseFilters,
  CourseResource,
  ApiError
} from '../../shared/types';
import { SmartEduClient } from '../../shared/services/SmartEduClient';
import { FilterService } from '../../shared/services/FilterService';
import { ResourceService } from '../../shared/services/ResourceService';

const { Header, Content, Footer } = Layout;
const { Title, Text, Paragraph } = Typography;

/**
 * 智慧教育下载器主应用界面
 * 集成筛选面板和资源列表，提供完整的教育资源下载体验
 */
export const SimpleMainApp: React.FC = () => {
  // 筛选和资源状态
  const [filters, setFilters] = useState<Partial<CourseFilters>>({});
  const [resources, setResources] = useState<CourseResource[]>([]);
  const [selectedResources, setSelectedResources] = useState<string[]>([]);
  const [searchLoading, setSearchLoading] = useState(false);

  // 创建服务实例
  const smartEduClient = useMemo(() => new SmartEduClient(), []);
  const filterService = useMemo(() => new FilterService(smartEduClient), [smartEduClient]);
  const resourceService = useMemo(() => new ResourceService(smartEduClient), [smartEduClient]);

  // 下载状态管理
  const [downloadTasks, setDownloadTasks] = useState<Map<string, any>>(new Map());



  /**
   * 处理筛选条件变更
   */
  const handleFilterChange = useCallback(async (newFilters: CourseFilters) => {
    console.log('筛选条件变更:', newFilters);
    setFilters(newFilters);

    // 检查筛选条件是否完整
    const isComplete = !!(newFilters.stage && newFilters.grade && newFilters.subject && newFilters.version && newFilters.volume);

    if (isComplete) {
      setSearchLoading(true);
      try {
        console.log('🔍 前端开始调用resourceService.searchResources');
        const searchResults = await resourceService.searchResources(newFilters);
        console.log('✅ 前端收到搜索结果:', {
          length: searchResults.length,
          preview: searchResults.slice(0, 3).map(r => ({ id: r.id, title: r.title }))
        });
        setResources(searchResults);
        setSelectedResources([]); // 清空选择
        message.success(`找到 ${searchResults.length} 个资源`);
      } catch (error) {
        console.error('搜索资源失败:', error);
        if (error instanceof ApiError) {
          message.error(`搜索失败: ${error.message}`);
        } else {
          message.error('搜索资源时发生未知错误');
        }
        setResources([]);
      } finally {
        setSearchLoading(false);
      }
    } else {
      // 筛选条件不完整时清空资源列表
      setResources([]);
      setSelectedResources([]);
    }
  }, [resourceService]);

  // 初始化下载功能
  useEffect(() => {
    console.log('🚀 初始化下载功能');

    // 清理函数
    return () => {
      console.log('🧹 清理下载功能');
    };
  }, []);

  /**
   * 处理单个资源下载
   */
  const handleResourceDownload = useCallback(async (resource: CourseResource) => {
    console.log('🔽 开始下载资源:', resource);

    try {
      message.loading('正在准备下载...', 1);

      // 检查Electron API是否可用
      if (!window.electronAPI?.startDownload) {
        throw new Error('下载功能不可用，请确保在Electron环境中运行');
      }

      // 使用Electron IPC启动下载
      const taskId = await window.electronAPI.startDownload(resource.url, {
        filename: `${resource.title}.${resource.type === 'video' ? 'mp4' : 'pdf'}`,
        directory: '/Users/<USER>/Downloads/智慧教育资源'
      });

      console.log('✅ 下载任务已启动:', {
        taskId,
        resourceTitle: resource.title,
        resourceType: resource.type,
        resourceUrl: resource.url
      });

      message.success(`下载已开始: ${resource.title}`);

      // 更新下载任务状态
      setDownloadTasks(prev => {
        const newTasks = new Map(prev);
        newTasks.set(taskId, {
          id: taskId,
          resource,
          status: 'downloading',
          progress: 0
        });
        return newTasks;
      });

      // 发送开始通知
      try {
        if (window.electronAPI?.showNotification) {
          window.electronAPI.showNotification(
            '下载开始',
            `正在下载: ${resource.title}`
          );
        }
      } catch (notificationError) {
        console.warn('通知发送失败:', notificationError);
      }

    } catch (error) {
      console.error('❌ 启动下载失败:', error);
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      message.error(`下载失败: ${errorMessage}`);
    }
  }, []);

  /**
   * 处理批量下载
   */
  const handleBatchDownload = useCallback(async (resources: CourseResource[]) => {
    console.log('🔽 开始批量下载资源:', resources);

    if (resources.length === 0) {
      message.warning('请选择要下载的资源');
      return;
    }

    try {
      message.loading(`正在准备下载 ${resources.length} 个资源...`, 1);

      // 检查Electron API是否可用
      if (!window.electronAPI?.startDownload) {
        throw new Error('下载功能不可用，请确保在Electron环境中运行');
      }

      // 逐个启动下载任务
      const taskIds: string[] = [];
      for (const resource of resources) {
        try {
          const taskId = await window.electronAPI.startDownload(resource.url, {
            filename: `${resource.title}.${resource.type === 'video' ? 'mp4' : 'pdf'}`,
            directory: '/Users/<USER>/Downloads/智慧教育资源'
          });
          taskIds.push(taskId);

          // 更新下载任务状态
          setDownloadTasks(prev => {
            const newTasks = new Map(prev);
            newTasks.set(taskId, {
              id: taskId,
              resource,
              status: 'downloading',
              progress: 0
            });
            return newTasks;
          });
        } catch (error) {
          console.error(`❌ 启动下载失败: ${resource.title}`, error);
        }
      }

      console.log('✅ 批量下载任务已启动:', {
        taskCount: taskIds.length,
        resources: resources.map(r => ({ title: r.title, type: r.type }))
      });

      message.success(`已启动 ${taskIds.length} 个下载任务`);

      // 发送开始通知
      try {
        if (window.electronAPI?.showNotification) {
          window.electronAPI.showNotification(
            '批量下载开始',
            `正在下载 ${taskIds.length} 个资源`
          );
        }
      } catch (notificationError) {
        console.warn('通知发送失败:', notificationError);
      }

    } catch (error) {
      console.error('❌ 批量下载失败:', error);
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      message.error(`批量下载失败: ${errorMessage}`);
    }
  }, []);

  /**
   * 处理资源选择变更
   */
  const handleSelectionChange = useCallback((selectedIds: string[]) => {
    setSelectedResources(selectedIds);
  }, []);



  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Header style={{ 
        background: '#fff', 
        padding: '0 24px', 
        borderBottom: '1px solid #f0f0f0',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between'
      }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Title level={3} style={{ margin: 0, color: '#1890ff' }}>
            📚 智慧教育资源下载器
          </Title>
          <Tag color="blue" style={{ marginLeft: '12px' }}>
            完整功能版
          </Tag>
          {resources.length > 0 && (
            <Tag color="green" style={{ marginLeft: '8px' }}>
              {resources.length} 个资源
            </Tag>
          )}
        </div>
        
        <Space>
          <Button type="primary" icon={<DownloadOutlined />}>
            已选择 {selectedResources.length} 个资源
          </Button>
        </Space>
      </Header>

      <Content style={{ padding: '24px' }}>
        <div style={{ maxWidth: '1400px', margin: '0 auto' }}>
          <Space direction="vertical" size="large" style={{ width: '100%' }}>

            {/* 筛选面板 */}
            <FilterPanel
              onFilterChange={handleFilterChange}
              loading={searchLoading}
              initialFilters={filters}
            />

            {/* 资源列表 */}
            {Object.keys(filters).length > 0 && (
              <Spin spinning={searchLoading}>
                <ResourceList
                  resources={resources}
                  onDownload={handleResourceDownload}
                  onBatchDownload={handleBatchDownload}
                  loading={searchLoading}
                  selectedResources={selectedResources}
                  onSelectionChange={handleSelectionChange}
                />
              </Spin>
            )}

            {/* 搜索提示 */}
            {Object.keys(filters).length === 0 && (
              <Card>
                <div style={{ textAlign: 'center', padding: '40px 0' }}>
                  <SearchOutlined
                    style={{
                      fontSize: '48px',
                      color: '#1890ff',
                      marginBottom: '16px'
                    }}
                  />
                  <Title level={3} style={{ color: '#1890ff', margin: '0 0 8px 0' }}>
                    开始搜索教育资源
                  </Title>
                  <Paragraph type="secondary">
                    请使用上方的筛选面板选择学段、年级、学科、版本和册次，
                    系统将为您展示相关的教育资源。
                  </Paragraph>
                </div>
              </Card>
            )}






          </Space>
        </div>
      </Content>

      <Footer style={{ textAlign: 'center', background: '#f0f2f5' }}>
        <Text type="secondary">
          智慧教育资源下载器 ©2024 - 集成筛选和资源管理功能
        </Text>
      </Footer>

      {/* DevTools 控制面板 */}
      <DevToolsControl />
    </Layout>
  );
};

export default SimpleMainApp;
