import { app, BrowserWindow, ipcMain, dialog, shell, Notification } from 'electron';
import * as path from 'path';
import * as fs from 'fs-extra';
import axios from 'axios';
import { AutoUpdater } from './services/AutoUpdater';
import { DevToolsManager } from './utils/devtools';

class SmartEduDownloaderApp {
  private mainWindow: BrowserWindow | null = null;
  private autoUpdater: AutoUpdater | null = null;
  private devToolsManager: DevToolsManager | null = null;

  constructor() {
    this.initializeApp();
  }

  private initializeApp(): void {
    // 设置全局错误处理
    process.on('uncaughtException', (error) => {
      console.error('Uncaught Exception:', error);
      // 不要因为 DevTools 错误而退出应用
      if (!error.message.includes('devtools://') && !error.message.includes('Failed to fetch')) {
        // 只有在非 DevTools 错误时才考虑退出
      }
    });

    process.on('unhandledRejection', (reason, promise) => {
      console.error('Unhandled Rejection at:', promise, 'reason:', reason);
    });

    // Handle app ready
    app.whenReady().then(() => {
      this.createMainWindow();
      this.setupIpcHandlers();

      app.on('activate', () => {
        if (BrowserWindow.getAllWindows().length === 0) {
          this.createMainWindow();
        }
      });
    });

    // Handle app window closed
    app.on('window-all-closed', () => {
      if (process.platform !== 'darwin') {
        app.quit();
      }
    });
  }

  private createMainWindow(): void {
    this.mainWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      minWidth: 800,
      minHeight: 600,
      webPreferences: {
        nodeIntegration: process.env.NODE_ENV === 'development', // 开发环境启用 Node.js 集成
        contextIsolation: process.env.NODE_ENV === 'production', // 生产环境启用上下文隔离
        preload: path.join(__dirname, 'preload.js'),
        webSecurity: process.env.NODE_ENV === 'production', // 开发环境禁用web安全以修复DevTools错误
        allowRunningInsecureContent: process.env.NODE_ENV === 'development',
        // enableRemoteModule 在新版本 Electron 中已被移除
      },
      titleBarStyle: 'default',
      show: false,
    });

    // 初始化 DevTools 管理器
    this.devToolsManager = new DevToolsManager(this.mainWindow);

    // 设置键盘快捷键
    this.setupKeyboardShortcuts();

    // Load the renderer
    if (process.env.NODE_ENV === 'development') {
      this.mainWindow.loadURL('http://localhost:3000');

      // 使用 DevTools 管理器安全地打开开发工具
      this.devToolsManager.openDevTools();
    } else {
      this.mainWindow.loadFile(path.join(__dirname, '../renderer/index.html'));
    }

    // Show window when ready
    this.mainWindow.once('ready-to-show', () => {
      this.mainWindow?.show();
      
      // 初始化自动更新（仅在生产环境）
      if (process.env.NODE_ENV !== 'development' && this.mainWindow) {
        this.autoUpdater = new AutoUpdater(this.mainWindow);
        // 启动后延迟5秒检查更新，避免影响启动速度
        setTimeout(() => {
          this.autoUpdater?.checkForUpdates();
        }, 5000);
      }
    });

    this.mainWindow.on('closed', () => {
      // 清理 DevTools 管理器
      if (this.devToolsManager) {
        this.devToolsManager.dispose();
        this.devToolsManager = null;
      }

      this.mainWindow = null;
      this.autoUpdater = null;
    });
  }

  /**
   * 设置键盘快捷键
   */
  private setupKeyboardShortcuts(): void {
    if (!this.mainWindow) return;

    // 监听键盘事件
    this.mainWindow.webContents.on('before-input-event', (event, input) => {
      // F12 或 Ctrl+Shift+I 切换 DevTools
      if (input.key === 'F12' ||
          (input.control && input.shift && input.key.toLowerCase() === 'i')) {
        if (process.env.NODE_ENV === 'development') {
          this.devToolsManager?.toggleDevTools();
        }
      }
    });
  }

  private setupIpcHandlers(): void {
    // App info handlers
    ipcMain.handle('app:getVersion', () => {
      return app.getVersion();
    });

    ipcMain.handle('app:getName', () => {
      return app.getName();
    });

    // Download related handlers
    ipcMain.handle('download:getDefaultPath', () => {
      return app.getPath('downloads');
    });

    ipcMain.handle('download:selectPath', async () => {
      const result = await dialog.showOpenDialog(this.mainWindow!, {
        properties: ['openDirectory'],
        title: '选择下载目录'
      });

      if (result.canceled) {
        return null;
      }

      return result.filePaths[0];
    });

    ipcMain.handle('download:openFolder', async (_, folderPath: string) => {
      try {
        await shell.openPath(folderPath);
        return true;
      } catch (error) {
        console.error('Failed to open folder:', error);
        return false;
      }
    });

    // File system handlers
    ipcMain.handle('fs:checkExists', async (_, filePath: string) => {
      try {
        return await fs.pathExists(filePath);
      } catch (error) {
        return false;
      }
    });

    ipcMain.handle('fs:getStats', async (_, filePath: string) => {
      try {
        const stats = await fs.stat(filePath);
        return {
          size: stats.size,
          isFile: stats.isFile(),
          isDirectory: stats.isDirectory(),
          mtime: stats.mtime,
          ctime: stats.ctime
        };
      } catch (error) {
        return null;
      }
    });

    // Notification handlers
    ipcMain.handle('notification:show', (_, title: string, body: string) => {
      if (Notification.isSupported()) {
        new Notification({
          title,
          body,
          icon: path.join(__dirname, '../assets/icon.png') // 如果有图标的话
        }).show();
        return true;
      }
      return false;
    });

    // Auto updater handlers
    ipcMain.handle('updater:checkForUpdates', () => {
      this.autoUpdater?.checkForUpdatesManually();
    });

    ipcMain.handle('updater:quitAndInstall', () => {
      this.autoUpdater?.quitAndInstall();
    });

    // DevTools handlers
    ipcMain.handle('devtools:toggle', () => {
      this.devToolsManager?.toggleDevTools();
    });

    ipcMain.handle('devtools:isOpened', () => {
      return this.devToolsManager?.isDevToolsOpened() || false;
    });

    // Download handlers (临时简化版本)
    ipcMain.handle('download:start', async (event, url: string, config: any) => {
      // 生成简单的任务 ID
      const taskId = `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      console.log(`开始下载任务: ${taskId}, URL: ${url}`);
      return taskId;
    });

    ipcMain.handle('download:pause', async (event, taskId: string) => {
      console.log(`暂停下载任务: ${taskId}`);
      return true;
    });

    ipcMain.handle('download:resume', async (event, taskId: string) => {
      console.log(`恢复下载任务: ${taskId}`);
      return true;
    });

    ipcMain.handle('download:cancel', async (event, taskId: string) => {
      console.log(`取消下载任务: ${taskId}`);
      return true;
    });

    ipcMain.handle('download:getAllTasks', async () => {
      // 返回空数组，后续可以实现真正的任务管理
      return [];
    });

    // API代理处理器 - 解决CORS问题
    ipcMain.handle('api:request', async (_, url: string, options: any = {}) => {
      try {
        console.log(`API代理请求: ${url}`);

        const response = await axios({
          url,
          method: options.method || 'GET',
          headers: {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            ...options.headers
          },
          timeout: 30000,
          ...options
        });

        return {
          success: true,
          data: response.data,
          status: response.status,
          headers: response.headers
        };
      } catch (error: any) {
        console.error(`API代理请求失败 ${url}:`, error.message);

        return {
          success: false,
          error: {
            message: error.message,
            status: error.response?.status,
            data: error.response?.data
          }
        };
      }
    });
  }
}

// Initialize the application
new SmartEduDownloaderApp();
