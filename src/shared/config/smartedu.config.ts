/**
 * 智慧教育平台配置文件
 * 集中管理API端点、标签维度等配置信息
 */

export interface SmartEduConfig {
  api: {
    baseURL: string;
    endpoints: {
      tags: string;
      materials: string;
      version: string;
      materialResources: string;
    };
  };
  tagDimensions: {
    stage: string;
    grade: string;
    subject: string;
    version: string;
    volume: string;
  };
  unitMapping: {
    patterns: {
      unitKeyword: RegExp;
      gardenKeyword: RegExp;
      oralKeyword: RegExp;
      lessonNumber: RegExp;
    };
    // 移除硬编码的关键词，改为从API数据中动态提取
    fallbackUnitOrder: string[];
  };
}

/**
 * 默认配置
 */
export const defaultSmartEduConfig: SmartEduConfig = {
  api: {
    baseURL: 'https://s-file-1.ykt.cbern.com.cn',
    endpoints: {
      tags: '/zxx/ndrs/tags/national_lesson_tag.json',
      materials: '/zxx/ndrs/national_lesson/teachingmaterials/part_100.json',
      version: '/zxx/ndrs/national_lesson/teachingmaterials/version/data_version.json',
      materialResources: '/zxx/ndrs/national_lesson/teachingmaterials/{materialId}/resources/part_100.json'
    }
  },
  tagDimensions: {
    stage: 'zxxxd',    // 学段
    grade: 'zxxnj',    // 年级
    subject: 'zxxxk',  // 学科
    version: 'zxxbb',  // 版本
    volume: 'zxxcc'    // 册次
  },
  unitMapping: {
    patterns: {
      unitKeyword: /第([一二三四五六七八九十\d]+)[单元]/,
      gardenKeyword: /语文园地([一二三四五六七八九十\d]*)/,
      oralKeyword: /口语交际|习作/,
      lessonNumber: /^(\d+)[.*·\s]/
    },
    // 移除硬编码关键词，改为从API数据中动态提取单元信息
    // 只保留回退排序，当API数据不完整时使用
    fallbackUnitOrder: ['第一单元', '第二单元', '第三单元', '第四单元', '第五单元', '第六单元', '第七单元', '第八单元', '其他']
  }
};

/**
 * 获取配置
 * 支持环境变量覆盖默认配置
 */
export function getSmartEduConfig(): SmartEduConfig {
  const config = { ...defaultSmartEduConfig };
  
  // 支持通过环境变量覆盖API基础URL
  if (process.env.SMARTEDU_API_BASE_URL) {
    config.api.baseURL = process.env.SMARTEDU_API_BASE_URL;
  }
  
  return config;
}
