import { 
  CourseResource, 
  ResourceDetail, 
  CourseFilters,
  ApiError,
  ValidationError
} from '../types';
import { SmartEduClient } from './SmartEduClient';

/**
 * 资源服务类
 * 提供资源搜索、详情获取、权限检查等功能
 */
export class ResourceService {
  private client: SmartEduClient;
  private cache: Map<string, { data: any; timestamp: number; ttl: number }> = new Map();
  private readonly defaultCacheTTL = 5 * 60 * 1000; // 5分钟缓存

  constructor(client: SmartEduClient) {
    this.client = client;
  }

  /**
   * 搜索资源
   */
  async searchResources(filters: CourseFilters): Promise<CourseResource[]> {
    this.validateFilters(filters);
    
    const cacheKey = `search:${JSON.stringify(filters)}`;
    const cached = this.getFromCache<CourseResource[]>(cacheKey);
    
    if (cached) {
      return cached;
    }

    try {
      const resources = await this.client.searchResources(filters);
      
      // 缓存搜索结果
      this.setCache(cacheKey, resources, this.defaultCacheTTL);
      
      return resources;
    } catch (error) {
      throw new ApiError('搜索资源失败', { 
        originalError: error as Error,
        context: { filters }
      });
    }
  }

  /**
   * 获取资源详情
   */
  async getResourceDetail(resourceId: string): Promise<ResourceDetail> {
    if (!resourceId) {
      throw new ValidationError('资源ID不能为空');
    }

    const cacheKey = `detail:${resourceId}`;
    const cached = this.getFromCache<ResourceDetail>(cacheKey);
    
    if (cached) {
      return cached;
    }

    try {
      const detail = await this.client.getResourceDetail(resourceId);
      
      // 缓存资源详情
      this.setCache(cacheKey, detail, this.defaultCacheTTL);
      
      return detail;
    } catch (error) {
      throw new ApiError('获取资源详情失败', { 
        originalError: error as Error,
        context: { resourceId }
      });
    }
  }

  /**
   * 检查资源访问权限
   */
  async checkResourceAccess(resourceId: string): Promise<{
    hasAccess: boolean;
    requiresAuth: boolean;
    accessLevel: 'public' | 'registered' | 'premium';
    message?: string;
  }> {
    if (!resourceId) {
      throw new ValidationError('资源ID不能为空');
    }

    const cacheKey = `access:${resourceId}`;
    const cached = this.getFromCache<{
      hasAccess: boolean;
      requiresAuth: boolean;
      accessLevel: 'public' | 'registered' | 'premium';
      message?: string;
    }>(cacheKey);
    
    if (cached) {
      return cached;
    }

    try {
      const accessInfo = await this.client.checkResourceAccess(resourceId);
      
      // 缓存访问权限信息（较短的缓存时间）
      this.setCache(cacheKey, accessInfo, 2 * 60 * 1000); // 2分钟
      
      return accessInfo;
    } catch (error) {
      throw new ApiError('检查资源访问权限失败', { 
        originalError: error as Error,
        context: { resourceId }
      });
    }
  }

  /**
   * 批量检查资源访问权限
   */
  async batchCheckResourceAccess(resourceIds: string[]): Promise<Record<string, {
    hasAccess: boolean;
    requiresAuth: boolean;
    accessLevel: 'public' | 'registered' | 'premium';
    message?: string;
  }>> {
    if (!resourceIds || resourceIds.length === 0) {
      return {};
    }

    const results: Record<string, any> = {};
    const uncachedIds: string[] = [];

    // 检查缓存
    for (const id of resourceIds) {
      const cacheKey = `access:${id}`;
      const cached = this.getFromCache<any>(cacheKey);
      
      if (cached) {
        results[id] = cached;
      } else {
        uncachedIds.push(id);
      }
    }

    // 批量获取未缓存的权限信息
    if (uncachedIds.length > 0) {
      const promises = uncachedIds.map(id => 
        this.checkResourceAccess(id).catch(error => ({
          hasAccess: false,
          requiresAuth: true,
          accessLevel: 'registered' as const,
          message: '权限检查失败'
        }))
      );

      const accessResults = await Promise.all(promises);
      
      uncachedIds.forEach((id, index) => {
        results[id] = accessResults[index];
      });
    }

    return results;
  }

  /**
   * 过滤可下载的资源
   */
  async filterDownloadableResources(
    resources: CourseResource[], 
    userLoggedIn: boolean = false
  ): Promise<{
    downloadable: CourseResource[];
    restricted: CourseResource[];
  }> {
    const downloadable: CourseResource[] = [];
    const restricted: CourseResource[] = [];

    for (const resource of resources) {
      try {
        const accessInfo = await this.checkResourceAccess(resource.id);
        
        if (accessInfo.hasAccess || (!accessInfo.requiresAuth)) {
          downloadable.push(resource);
        } else if (userLoggedIn && accessInfo.accessLevel !== 'premium') {
          downloadable.push(resource);
        } else {
          restricted.push(resource);
        }
      } catch (error) {
        // 如果权限检查失败，根据用户登录状态和资源要求决定
        if (!resource.requiresAuth || (userLoggedIn && resource.accessLevel !== 'premium')) {
          downloadable.push(resource);
        } else {
          restricted.push(resource);
        }
      }
    }

    return { downloadable, restricted };
  }

  /**
   * 获取资源统计信息
   */
  getResourceStats(resources: CourseResource[]): {
    total: number;
    byType: Record<string, number>;
    byAccessLevel: Record<string, number>;
    requiresAuth: number;
    public: number;
  } {
    const stats = {
      total: resources.length,
      byType: {} as Record<string, number>,
      byAccessLevel: {} as Record<string, number>,
      requiresAuth: 0,
      public: 0
    };

    resources.forEach(resource => {
      // 按类型统计
      stats.byType[resource.type] = (stats.byType[resource.type] || 0) + 1;
      
      // 按访问级别统计
      stats.byAccessLevel[resource.accessLevel] = (stats.byAccessLevel[resource.accessLevel] || 0) + 1;
      
      // 权限统计
      if (resource.requiresAuth) {
        stats.requiresAuth++;
      } else {
        stats.public++;
      }
    });

    return stats;
  }

  /**
   * 验证筛选条件
   */
  private validateFilters(filters: CourseFilters): void {
    const requiredFields: (keyof CourseFilters)[] = ['stage', 'grade', 'subject', 'version', 'volume'];
    
    for (const field of requiredFields) {
      if (!filters[field]) {
        throw new ValidationError(`筛选条件中缺少必需字段: ${field}`);
      }
    }
  }

  /**
   * 从缓存获取数据
   */
  private getFromCache<T>(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }
    
    const now = Date.now();
    if (now > entry.timestamp + entry.ttl) {
      this.cache.delete(key);
      return null;
    }
    
    return entry.data as T;
  }

  /**
   * 设置缓存
   */
  private setCache<T>(key: string, data: T, ttl: number = this.defaultCacheTTL): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }

  /**
   * 清除缓存
   */
  clearCache(pattern?: string): void {
    if (!pattern) {
      this.cache.clear();
      return;
    }
    
    const keysToDelete: string[] = [];
    for (const key of this.cache.keys()) {
      if (key.includes(pattern)) {
        keysToDelete.push(key);
      }
    }
    
    keysToDelete.forEach(key => this.cache.delete(key));
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats(): {
    size: number;
    keys: string[];
    memoryUsage: number;
  } {
    const keys = Array.from(this.cache.keys());
    const memoryUsage = JSON.stringify(Array.from(this.cache.entries())).length;
    
    return {
      size: this.cache.size,
      keys,
      memoryUsage
    };
  }
}

export default ResourceService;