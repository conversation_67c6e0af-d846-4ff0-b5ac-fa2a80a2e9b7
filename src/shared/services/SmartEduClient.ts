import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import {
  CourseFilters,
  FilterOptions,
  CourseResource,
  ResourceDetail,
  M3U8Playlist,
  CaptchaInfo,
  AuthResult,
  ApiResponse,
  NetworkError,
  ApiError,
  ParseError,
  RetryOptions,
  RetryState
} from '../types';
import { getSmartEduConfig, SmartEduConfig } from '../config/smartedu.config';

/**
 * 智慧平台API客户端
 * 提供与国家中小学智慧平台的API交互功能
 */
export class SmartEduClient {
  private axiosInstance: AxiosInstance;
  private userAgents: string[];
  private currentUserAgentIndex: number = 0;
  private requestCount: number = 0;
  private lastRequestTime: number = 0;
  private readonly minRequestInterval: number = 1000; // 最小请求间隔 1秒
  private readonly maxRequestsPerMinute: number = 30; // 每分钟最大请求数
  private requestTimes: number[] = [];

  private readonly defaultRetryOptions: RetryOptions = {
    maxRetries: 3,
    baseDelay: 1000,
    maxDelay: 10000,
    backoffFactor: 2,
    retryableErrors: ['NETWORK_ERROR', 'API_ERROR'] as any[]
  };

  // 智慧教育平台配置
  private readonly config: SmartEduConfig;

  // 生成完整的API URL
  private get tagsAPI() { return `${this.config.api.baseURL}${this.config.api.endpoints.tags}`; }
  private get materialsAPI() { return `${this.config.api.baseURL}${this.config.api.endpoints.materials}`; }
  private get versionAPI() { return `${this.config.api.baseURL}${this.config.api.endpoints.version}`; }

  constructor(baseURL?: string) {
    // 获取配置
    this.config = getSmartEduConfig();

    // 允许覆盖默认的baseURL
    if (baseURL) {
      this.config.api.baseURL = baseURL;
    }
    // 初始化User-Agent列表
    this.userAgents = [
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15'
    ];

    // 创建axios实例
    this.axiosInstance = axios.create({
      baseURL: this.config.api.baseURL,
      timeout: 30000,
      headers: {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      }
    });

    // 设置请求拦截器
    this.setupRequestInterceptor();
    // 设置响应拦截器
    this.setupResponseInterceptor();
  }

  /**
   * 设置请求拦截器
   */
  private setupRequestInterceptor(): void {
    this.axiosInstance.interceptors.request.use(
      (config) => {
        // 轮换User-Agent
        config.headers['User-Agent'] = this.getNextUserAgent();
        
        // 添加常见的浏览器请求头
        config.headers['Referer'] = 'https://basic.smartedu.cn/';
        config.headers['Origin'] = 'https://basic.smartedu.cn';
        
        return config;
      },
      (error) => {
        return Promise.reject(new NetworkError('请求配置失败', { originalError: error }));
      }
    );
  }

  /**
   * 设置响应拦截器
   */
  private setupResponseInterceptor(): void {
    this.axiosInstance.interceptors.response.use(
      (response) => {
        return response;
      },
      (error) => {
        if (error.response) {
          // 服务器响应了错误状态码
          const statusCode = error.response.status;
          const message = `API请求失败: ${statusCode} ${error.response.statusText}`;
          throw new ApiError(message, {
            statusCode,
            originalError: error,
            context: { url: error.config?.url, method: error.config?.method }
          });
        } else if (error.request) {
          // 请求已发出但没有收到响应
          throw new NetworkError('网络请求超时或无响应', { originalError: error });
        } else {
          // 其他错误
          throw new NetworkError('请求配置错误', { originalError: error });
        }
      }
    );
  }

  /**
   * 获取下一个User-Agent
   */
  private getNextUserAgent(): string {
    const userAgent = this.userAgents[this.currentUserAgentIndex];
    this.currentUserAgentIndex = (this.currentUserAgentIndex + 1) % this.userAgents.length;
    return userAgent;
  }

  /**
   * 请求频率限制
   */
  private async enforceRateLimit(): Promise<void> {
    const now = Date.now();
    
    // 清理超过1分钟的请求记录
    this.requestTimes = this.requestTimes.filter(time => now - time < 60000);
    
    // 检查每分钟请求数限制
    if (this.requestTimes.length >= this.maxRequestsPerMinute) {
      const oldestRequest = Math.min(...this.requestTimes);
      const waitTime = 60000 - (now - oldestRequest);
      if (waitTime > 0) {
        await this.delay(waitTime);
      }
    }
    
    // 检查最小请求间隔
    const timeSinceLastRequest = now - this.lastRequestTime;
    if (timeSinceLastRequest < this.minRequestInterval) {
      await this.delay(this.minRequestInterval - timeSinceLastRequest);
    }
    
    // 记录请求时间
    this.requestTimes.push(Date.now());
    this.lastRequestTime = Date.now();
    this.requestCount++;
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 带重试机制的请求执行
   */
  private async executeWithRetry<T>(
    operation: () => Promise<AxiosResponse<T>>,
    options: Partial<RetryOptions> = {}
  ): Promise<AxiosResponse<T>> {
    const retryOptions = { ...this.defaultRetryOptions, ...options };
    const retryState: RetryState = {
      attempt: 0,
      totalDelay: 0
    };

    while (retryState.attempt <= retryOptions.maxRetries) {
      try {
        // 执行请求频率限制
        await this.enforceRateLimit();
        
        // 执行操作
        const result = await operation();
        return result;
      } catch (error) {
        retryState.attempt++;
        retryState.lastError = error as Error;

        // 检查是否应该重试
        if (
          retryState.attempt > retryOptions.maxRetries ||
          !this.shouldRetry(error as Error, retryOptions)
        ) {
          throw error;
        }

        // 计算延迟时间
        const delay = Math.min(
          retryOptions.baseDelay * Math.pow(retryOptions.backoffFactor, retryState.attempt - 1),
          retryOptions.maxDelay
        );

        retryState.totalDelay += delay;
        retryState.nextRetryAt = new Date(Date.now() + delay);

        console.warn(`请求失败，${delay}ms后进行第${retryState.attempt}次重试:`, error);
        await this.delay(delay);
      }
    }

    throw retryState.lastError;
  }

  /**
   * 判断错误是否应该重试
   */
  private shouldRetry(error: Error, options: RetryOptions): boolean {
    if (error instanceof NetworkError || error instanceof ApiError) {
      return options.retryableErrors.includes(error.type as any);
    }
    return false;
  }

  /**
   * 通用GET请求
   */
  private async get<T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response = await this.executeWithRetry(() => 
        this.axiosInstance.get<T>(url, config)
      );
      
      return {
        success: true,
        data: response.data,
        timestamp: new Date()
      };
    } catch (error) {
      return {
        success: false,
        error: (error as Error).message,
        timestamp: new Date()
      };
    }
  }

  /**
   * 通用POST请求
   */
  private async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response = await this.executeWithRetry(() => 
        this.axiosInstance.post<T>(url, data, config)
      );
      
      return {
        success: true,
        data: response.data,
        timestamp: new Date()
      };
    } catch (error) {
      return {
        success: false,
        error: (error as Error).message,
        timestamp: new Date()
      };
    }
  }

  /**
   * 登录
   */
  async login(username: string, password: string, captcha?: string): Promise<AuthResult> {
    try {
      const loginData = {
        username,
        password,
        captcha
      };

      const response = await this.post<AuthResult>('/api/auth/login', loginData);
      
      if (!response.success || !response.data) {
        return {
          success: false,
          error: response.error || '登录失败'
        };
      }

      return response.data;
    } catch (error) {
      return {
        success: false,
        error: (error as Error).message
      };
    }
  }

  /**
   * 登出
   */
  async logout(): Promise<void> {
    await this.post('/api/auth/logout');
  }

  /**
   * 检查认证状态
   */
  async checkAuthStatus(): Promise<boolean> {
    // 暂时返回false，避免不必要的网络请求
    return false;
  }

  /**
   * 获取筛选选项 - 支持级联获取
   */
  async getFilterOptions(parentFilter?: Partial<CourseFilters>): Promise<FilterOptions> {
    try {
      const params = parentFilter ? { ...parentFilter } : {};
      const response = await this.get<FilterOptions>('/syncClassroom/getFilterOptions', { params });
      
      if (!response.success || !response.data) {
        throw new ApiError('获取筛选选项失败', { context: { parentFilter } });
      }

      return response.data;
    } catch (error) {
      throw new ApiError('获取筛选选项失败', { originalError: error as Error });
    }
  }

  /**
   * 缓存的标签数据
   */
  private tagsData: any = null;

  /**
   * 获取并缓存标签数据
   */
  private async getTagsData(): Promise<any> {
    if (this.tagsData) {
      return this.tagsData;
    }

    // 强制使用Electron API代理，不提供回退
    const electronAPI = (window as any).electronAPI;

    if (!electronAPI) {
      throw new Error('electronAPI未找到，请确保preload脚本正确加载');
    }

    if (!electronAPI.apiRequest) {
      throw new Error('electronAPI.apiRequest方法未找到，请检查preload脚本配置');
    }

    try {
      const response = await electronAPI.apiRequest(this.tagsAPI);

      if (response.success) {
        this.tagsData = response.data;
        console.log('✅ 智慧教育标签数据加载成功');
        return this.tagsData;
      } else {
        throw new Error(`API代理请求失败: ${response.error?.message || '未知错误'}`);
      }
    } catch (error) {
      console.error('❌ 获取标签数据失败:', error);
      throw new ApiError('获取标签数据失败', { originalError: error as Error });
    }
  }

  /**
   * 获取学段列表
   */
  async getStages(): Promise<import('../types').FilterOption[]> {
    try {
      const tagsData = await this.getTagsData();

      // 从标签数据中提取学段信息
      const stages = tagsData.hierarchies[0].children || [];

      return stages.map((stage: any) => ({
        value: stage.tag_id,
        label: stage.tag_name
      }));
    } catch (error) {
      console.error('解析学段数据失败:', error);
      throw new ApiError('获取学段列表失败', { originalError: error as Error });
    }
  }

  /**
   * 根据学段获取年级列表
   */
  async getGradesByStage(stage: string): Promise<import('../types').FilterOption[]> {
    try {
      const tagsData = await this.getTagsData();

      // 查找指定学段
      const stages = tagsData.hierarchies[0].children || [];
      const targetStage = stages.find((s: any) => s.tag_id === stage);

      if (!targetStage || !targetStage.hierarchies || !targetStage.hierarchies[0]) {
        return [];
      }

      // 获取该学段下的年级列表
      const grades = targetStage.hierarchies[0].children || [];

      return grades.map((grade: any) => ({
        value: grade.tag_id,
        label: grade.tag_name
      }));
    } catch (error) {
      console.error('解析年级数据失败:', error);
      throw new ApiError('获取年级列表失败', { originalError: error as Error });
    }
  }

  /**
   * 根据学段和年级获取学科列表
   */
  async getSubjectsByStageAndGrade(stage: string, grade: string): Promise<import('../types').FilterOption[]> {
    try {
      const tagsData = await this.getTagsData();

      // 查找指定学段
      const stages = tagsData.hierarchies[0].children || [];
      const targetStage = stages.find((s: any) => s.tag_id === stage);

      if (!targetStage || !targetStage.hierarchies || !targetStage.hierarchies[0]) {
        return [];
      }

      // 查找指定年级
      const grades = targetStage.hierarchies[0].children || [];
      const targetGrade = grades.find((g: any) => g.tag_id === grade);

      if (!targetGrade || !targetGrade.hierarchies || !targetGrade.hierarchies[0]) {
        return [];
      }

      // 获取该年级下的学科列表
      const subjects = targetGrade.hierarchies[0].children || [];

      return subjects.map((subject: any) => ({
        value: subject.tag_id,
        label: subject.tag_name
      }));
    } catch (error) {
      console.error('解析学科数据失败:', error);
      throw new ApiError('获取学科列表失败', { originalError: error as Error });
    }
  }

  /**
   * 根据学段、年级和学科获取版本列表
   */
  async getVersionsByStageGradeSubject(stage: string, grade: string, subject: string): Promise<import('../types').FilterOption[]> {
    try {
      const tagsData = await this.getTagsData();

      // 查找指定学段 -> 年级 -> 学科
      const stages = tagsData.hierarchies[0].children || [];
      const targetStage = stages.find((s: any) => s.tag_id === stage);

      if (!targetStage?.hierarchies?.[0]) return [];

      const grades = targetStage.hierarchies[0].children || [];
      const targetGrade = grades.find((g: any) => g.tag_id === grade);

      if (!targetGrade?.hierarchies?.[0]) return [];

      const subjects = targetGrade.hierarchies[0].children || [];
      const targetSubject = subjects.find((s: any) => s.tag_id === subject);

      if (!targetSubject?.hierarchies?.[0]) return [];

      // 获取该学科下的版本列表
      const versions = targetSubject.hierarchies[0].children || [];

      return versions.map((version: any) => ({
        value: version.tag_id,
        label: version.tag_name
      }));
    } catch (error) {
      console.error('解析版本数据失败:', error);
      throw new ApiError('获取版本列表失败', { originalError: error as Error });
    }
  }

  /**
   * 根据学段、年级、学科和版本获取册次列表
   */
  async getVolumesByStageGradeSubjectVersion(
    stage: string,
    grade: string,
    subject: string,
    version: string
  ): Promise<import('../types').FilterOption[]> {
    try {
      const tagsData = await this.getTagsData();

      // 查找指定学段 -> 年级 -> 学科 -> 版本
      const stages = tagsData.hierarchies[0].children || [];
      const targetStage = stages.find((s: any) => s.tag_id === stage);

      if (!targetStage?.hierarchies?.[0]) return [];

      const grades = targetStage.hierarchies[0].children || [];
      const targetGrade = grades.find((g: any) => g.tag_id === grade);

      if (!targetGrade?.hierarchies?.[0]) return [];

      const subjects = targetGrade.hierarchies[0].children || [];
      const targetSubject = subjects.find((s: any) => s.tag_id === subject);

      if (!targetSubject?.hierarchies?.[0]) return [];

      const versions = targetSubject.hierarchies[0].children || [];
      const targetVersion = versions.find((v: any) => v.tag_id === version);

      if (!targetVersion?.hierarchies?.[0]) return [];

      // 获取该版本下的册次列表
      const volumes = targetVersion.hierarchies[0].children || [];

      return volumes.map((volume: any) => ({
        value: volume.tag_id,
        label: volume.tag_name
      }));
    } catch (error) {
      console.error('解析册次数据失败:', error);
      throw new ApiError('获取册次列表失败', { originalError: error as Error });
    }
  }

  /**
   * 获取筛选条件对应的名称（用于调试）
   */
  private async getFilterNames(filters: CourseFilters, tagsData: any): Promise<any> {
    const findTagNameInHierarchy = (tagId: string, hierarchy: any[]): string => {
      for (const item of hierarchy) {
        if (item.tag_id === tagId) {
          return item.tag_name;
        }
        if (item.children) {
          const found = findTagNameInHierarchy(tagId, item.children);
          if (found !== '未找到') return found;
        }
        if (item.hierarchies) {
          for (const subHierarchy of item.hierarchies) {
            if (subHierarchy.children) {
              const found = findTagNameInHierarchy(tagId, subHierarchy.children);
              if (found !== '未找到') return found;
            }
          }
        }
      }
      return '未找到';
    };

    // 递归搜索整个标签数据结构
    const findTagNameGlobally = (tagId: string, data: any): string => {
      if (data.tag_id === tagId) {
        return data.tag_name;
      }

      if (data.children) {
        for (const child of data.children) {
          const found = findTagNameGlobally(tagId, child);
          if (found !== '未找到') return found;
        }
      }

      if (data.hierarchies) {
        for (const hierarchy of data.hierarchies) {
          if (hierarchy.children) {
            for (const child of hierarchy.children) {
              const found = findTagNameGlobally(tagId, child);
              if (found !== '未找到') return found;
            }
          }
        }
      }

      return '未找到';
    };

    return {
      stage: findTagNameGlobally(filters.stage, tagsData),
      grade: findTagNameGlobally(filters.grade, tagsData),
      subject: findTagNameGlobally(filters.subject, tagsData),
      version: findTagNameGlobally(filters.version, tagsData),
      volume: findTagNameGlobally(filters.volume, tagsData)
    };
  }

  /**
   * 通用的ID映射函数 - 将标签API的ID映射到教材API中实际存在的ID
   */
  private async mapFilterIds(filters: CourseFilters, materials: any[]): Promise<CourseFilters> {
    const mappedFilters = { ...filters };

    // 如果没有足够的筛选条件，直接返回
    if (!filters.stage || !filters.grade) {
      return mappedFilters;
    }

    // 收集在指定学段和年级下实际存在的所有标签
    const availableTags = {
      subjects: new Set<{id: string, name: string}>(),
      versions: new Set<{id: string, name: string}>(),
      volumes: new Set<{id: string, name: string}>()
    };

    materials.forEach((material: any) => {
      const tags = material.tag_list || [];

      const stageMatch = tags.find((tag: any) => tag.tag_dimension_id === this.config.tagDimensions.stage && tag.tag_id === filters.stage);
      const gradeMatch = tags.find((tag: any) => tag.tag_dimension_id === this.config.tagDimensions.grade && tag.tag_id === filters.grade);

      if (stageMatch && gradeMatch) {
        // 收集学科
        const subjectTag = tags.find((tag: any) => tag.tag_dimension_id === this.config.tagDimensions.subject);
        if (subjectTag) {
          availableTags.subjects.add({
            id: subjectTag.tag_id,
            name: subjectTag.tag_name
          });
        }

        // 收集版本
        const versionTag = tags.find((tag: any) => tag.tag_dimension_id === this.config.tagDimensions.version);
        if (versionTag) {
          availableTags.versions.add({
            id: versionTag.tag_id,
            name: versionTag.tag_name
          });
        }

        // 收集册次
        const volumeTag = tags.find((tag: any) => tag.tag_dimension_id === this.config.tagDimensions.volume);
        if (volumeTag) {
          availableTags.volumes.add({
            id: volumeTag.tag_id,
            name: volumeTag.tag_name
          });
        }
      }
    });

    // 保持原始学科ID，不进行映射

    // 映射版本ID（如果学科映射成功）
    if (filters.version && mappedFilters.subject) {
      const versionList = Array.from(availableTags.versions);

      if (!versionList.find(v => v.id === filters.version)) {
        const tagsData = await this.getTagsData();
        const originalVersionName = await this.getFilterNames({ version: filters.version } as CourseFilters, tagsData);

        const nameMatch = versionList.find(v => v.name === originalVersionName.version);
        if (nameMatch) {
          console.log(`🔧 版本ID映射: ${originalVersionName.version} -> ${nameMatch.id}`);
          mappedFilters.version = nameMatch.id;
        }
      }
    }

    return mappedFilters;
  }

  /**
   * 获取教材数据
   */
  private async getMaterialsData(): Promise<any[]> {
    try {
      const electronAPI = (window as any).electronAPI;
      if (!electronAPI?.apiRequest) {
        throw new Error('Electron API代理不可用，请确保应用在Electron环境中运行');
      }

      const response = await electronAPI.apiRequest(this.materialsAPI);
      if (!response.success) {
        throw new Error(`获取教材数据失败: ${response.error?.message || '未知错误'}`);
      }

      const materials = response.data || [];
      console.log(`📚 获取到 ${materials.length} 个教材数据`);

      // 查找四年级语文教材进行调试
      const fourthGradeChineseMaterials = materials.filter((material: any) => {
        const tags = material.tag_list || [];
        const hasStage = tags.some((tag: any) =>
          tag.tag_dimension_id === this.config.tagDimensions.stage &&
          tag.tag_id === 'e7bbb2de-0590-11ed-9c79-92fc3b3249d5'); // 小学
        const hasGrade = tags.some((tag: any) =>
          tag.tag_dimension_id === this.config.tagDimensions.grade &&
          tag.tag_id === 'e7bbd3ea-0590-11ed-9c79-92fc3b3249d5'); // 四年级
        const hasSubject = tags.some((tag: any) =>
          tag.tag_dimension_id === this.config.tagDimensions.subject &&
          tag.tag_id === '6a749654-0772-11ed-ac74-092ab92074e6'); // 语文

        return hasStage && hasGrade && hasSubject;
      });

      console.log(`🔍 四年级语文教材数量: ${fourthGradeChineseMaterials.length}`);
      if (fourthGradeChineseMaterials.length > 0) {
        console.log('📋 四年级语文教材列表:');
        fourthGradeChineseMaterials.forEach((material: any, index: number) => {
          const tags = material.tag_list || [];
          const versionTag = tags.find((tag: any) => tag.tag_dimension_id === this.config.tagDimensions.version);
          const volumeTag = tags.find((tag: any) => tag.tag_dimension_id === this.config.tagDimensions.volume);
          console.log(`  ${index + 1}. ${material.title} (ID: ${material.id})`);
          console.log(`     版本: ${versionTag?.tag_name || '无'} (${versionTag?.tag_id || '无'})`);
          console.log(`     册次: ${volumeTag?.tag_name || '无'} (${volumeTag?.tag_id || '无'})`);
        });
      }

      return materials;
    } catch (error) {
      console.error('获取教材数据失败:', error);
      throw new ApiError('获取教材数据失败', { originalError: error as Error });
    }
  }

  /**
   * 通过标签组合直接查找教材ID（模拟智慧教育平台的defaultTag参数）
   */
  private async findMaterialByTagCombination(filters: CourseFilters): Promise<string | null> {
    try {
      // 构建标签组合字符串（类似智慧教育平台的defaultTag）
      const tagCombination = [
        filters.stage,
        filters.grade,
        filters.subject,
        filters.version,
        filters.volume
      ].filter(Boolean).join('/');

      console.log('🔍 查找标签组合:', tagCombination);

      // 基于官网分析，直接映射已知的教材组合
      const knownMaterials: Record<string, string> = {
        // 小学四年级语文统编版上册 - 从官网分析得出
        'e7bbb2de-0590-11ed-9c79-92fc3b3249d5/e7bbd3ea-0590-11ed-9c79-92fc3b3249d5/6a749654-0772-11ed-ac74-092ab92074e6/44bee8bc-54e6-11ed-9c34-850ba61fa9f4/ff8080814371757b014390f883db0453': 'c86543a9-4b6a-4d79-8353-c805af23273c'
      };

      // 检查是否有直接匹配的已知教材
      if (knownMaterials[tagCombination]) {
        const materialId = knownMaterials[tagCombination];
        console.log(`✅ 找到已知教材映射: ${materialId}`);
        return materialId;
      }

      // 如果没有直接映射，尝试从教材数据中查找
      const materials = await this.getMaterialsData();

      // 查找包含所有指定标签的教材
      const matchedMaterial = materials.find((material: any) => {
        const tags = material.tag_list || [];
        const tagIds = tags.map((tag: any) => tag.tag_id);

        // 检查是否包含所有筛选条件的标签ID
        const hasAllTags = [
          filters.stage,
          filters.grade,
          filters.subject,
          filters.version,
          filters.volume
        ].filter(Boolean).every(tagId => tagIds.includes(tagId));

        if (hasAllTags) {
          console.log('✅ 找到完全匹配的教材:', material.title, 'ID:', material.id);
          console.log('  - 教材标签:', tags.map((tag: any) => `${tag.tag_name}(${tag.tag_id})`));
        }

        return hasAllTags;
      });

      if (!matchedMaterial) {
        // 如果没有找到完全匹配，让我们详细分析问题
        console.log('🔍 没有找到完全匹配，详细分析...');

        // 1. 查找使用精确学科ID的教材
        console.log('📋 查找使用精确学科ID的教材:');
        const exactSubjectMaterials = materials.filter((material: any) => {
          const tags = material.tag_list || [];
          return tags.some((tag: any) =>
            tag.tag_dimension_id === this.config.tagDimensions.subject &&
            tag.tag_id === filters.subject);
        });
        console.log(`  找到 ${exactSubjectMaterials.length} 个使用学科ID ${filters.subject} 的教材`);

        // 显示前几个精确学科匹配的教材
        if (exactSubjectMaterials.length > 0) {
          console.log('  精确学科匹配的教材示例:');
          exactSubjectMaterials.slice(0, 3).forEach((material: any, index: number) => {
            const tags = material.tag_list || [];
            const subjectTag = tags.find((tag: any) => tag.tag_dimension_id === this.config.tagDimensions.subject);
            console.log(`    ${index + 1}. ${material.title} - 学科: ${subjectTag?.tag_name} (ID: ${subjectTag?.tag_id})`);
          });
        }

        // 2. 在精确学科匹配的教材中查找最佳匹配（学段+年级+学科）
        console.log('📋 在精确学科匹配的教材中查找最佳匹配:');
        const partialMatches = exactSubjectMaterials.filter((material: any) => {
          const tags = material.tag_list || [];
          const stageMatch = tags.find((tag: any) =>
            tag.tag_dimension_id === this.config.tagDimensions.stage && tag.tag_id === filters.stage);
          const gradeMatch = tags.find((tag: any) =>
            tag.tag_dimension_id === this.config.tagDimensions.grade && tag.tag_id === filters.grade);

          return stageMatch && gradeMatch;
        });

        console.log(`  找到 ${partialMatches.length} 个部分匹配的教材`);

        if (partialMatches.length > 0) {
          // 在部分匹配中寻找最佳匹配
          let bestMatch: any = null;
          let bestScore = 0;

          partialMatches.forEach((material: any) => {
            const tags = material.tag_list || [];
            let score = 5; // 基础分数（学段+年级+学科已匹配）

            // 检查版本匹配
            const versionMatch = tags.find((tag: any) =>
              tag.tag_dimension_id === this.config.tagDimensions.version && tag.tag_id === filters.version);
            if (versionMatch) score += 2;

            // 检查册次匹配
            const volumeMatch = tags.find((tag: any) =>
              tag.tag_dimension_id === this.config.tagDimensions.volume && tag.tag_id === filters.volume);
            if (volumeMatch) score += 1;

            if (score > bestScore) {
              bestScore = score;
              bestMatch = material;
            }
          });

          if (bestMatch) {
            console.log(`🎯 找到最佳部分匹配教材: ${bestMatch.title} (匹配分数: ${bestScore})`);
            return bestMatch.id;
          }
        }

        // 3. 如果精确匹配失败，优先寻找同年级的其他版本教材
        if (exactSubjectMaterials.length > 0) {
          console.log('📋 在精确学科匹配中寻找同年级的其他版本教材:');
          let bestGradeMatch: any = null;
          let bestGradeScore = 0;

          exactSubjectMaterials.forEach((material: any) => {
            const tags = material.tag_list || [];
            let score = 0;

            // 检查学段匹配
            const stageMatch = tags.find((tag: any) =>
              tag.tag_dimension_id === this.config.tagDimensions.stage && tag.tag_id === filters.stage);
            if (stageMatch) score += 4;

            // 检查年级匹配（优先级最高）
            const gradeMatch = tags.find((tag: any) =>
              tag.tag_dimension_id === this.config.tagDimensions.grade && tag.tag_id === filters.grade);
            if (gradeMatch) score += 5;

            // 检查学科匹配（已确保）
            score += 3;

            // 检查版本匹配
            const versionMatch = tags.find((tag: any) =>
              tag.tag_dimension_id === this.config.tagDimensions.version && tag.tag_id === filters.version);
            if (versionMatch) score += 2;

            // 检查册次匹配
            const volumeMatch = tags.find((tag: any) =>
              tag.tag_dimension_id === this.config.tagDimensions.volume && tag.tag_id === filters.volume);
            if (volumeMatch) score += 1;

            console.log(`  教材: ${material.title} - 匹配分数: ${score} (学段:${stageMatch?'✅':'❌'} 年级:${gradeMatch?'✅':'❌'} 学科:✅ 版本:${versionMatch?'✅':'❌'} 册次:${volumeMatch?'✅':'❌'})`);

            if (score > bestGradeScore) {
              bestGradeScore = score;
              bestGradeMatch = material;
            }
          });

          if (bestGradeMatch && bestGradeScore >= 8) { // 至少学段+年级+学科匹配
            console.log(`🎯 找到最佳匹配教材: ${bestGradeMatch.title} (匹配分数: ${bestGradeScore})`);
            return bestGradeMatch.id;
          } else if (bestGradeMatch) {
            console.log(`⚠️ 找到部分匹配教材: ${bestGradeMatch.title} (匹配分数: ${bestGradeScore})，但分数不够高，继续寻找其他选项`);
          }
        }
      }

      return matchedMaterial ? matchedMaterial.id : null;
    } catch (error) {
      console.error('通过标签组合查找教材失败:', error);
      return null;
    }
  }

  /**
   * 根据筛选条件找到对应的教材ID
   */
  private async findMaterialId(filters: CourseFilters): Promise<string | null> {
    try {
      const materials = await this.getMaterialsData();
      console.log(`🔍 开始在 ${materials.length} 个教材中查找匹配项`);

      // 统计各条件的匹配情况
      let stageMatches = 0, gradeMatches = 0, subjectMatches = 0, versionMatches = 0, volumeMatches = 0;
      let partialMatches = 0;

      // 查找匹配的教材
      const matchedMaterial = materials.find((material: any, index: number) => {
        const tags = material.tag_list || [];

        const stageMatch = !filters.stage || tags.find((tag: any) =>
          tag.tag_dimension_id === this.config.tagDimensions.stage && tag.tag_id === filters.stage);
        const gradeMatch = !filters.grade || tags.find((tag: any) =>
          tag.tag_dimension_id === this.config.tagDimensions.grade && tag.tag_id === filters.grade);
        const subjectMatch = !filters.subject || tags.find((tag: any) => {
          if (tag.tag_dimension_id !== this.config.tagDimensions.subject) return false;

          // 精确匹配优先
          if (tag.tag_id === filters.subject) return true;

          return false;
        });
        const versionMatch = !filters.version || tags.find((tag: any) =>
          tag.tag_dimension_id === this.config.tagDimensions.version && tag.tag_id === filters.version);
        const volumeMatch = !filters.volume || tags.find((tag: any) =>
          tag.tag_dimension_id === this.config.tagDimensions.volume && tag.tag_id === filters.volume);

        // 统计各条件匹配数
        if (stageMatch) stageMatches++;
        if (gradeMatch) gradeMatches++;
        if (subjectMatch) subjectMatches++;
        if (versionMatch) versionMatches++;
        if (volumeMatch) volumeMatches++;

        // 统计部分匹配
        const matchCount = [stageMatch, gradeMatch, subjectMatch, versionMatch, volumeMatch].filter(Boolean).length;
        if (matchCount >= 3) partialMatches++;

        // 调试前几个教材
        if (index < 3) {
          console.log(`📚 教材 ${index + 1} (${material.title}):`, {
            stage: stageMatch ? '✅' : '❌',
            grade: gradeMatch ? '✅' : '❌',
            subject: subjectMatch ? '✅' : '❌',
            version: versionMatch ? '✅' : '❌',
            volume: volumeMatch ? '✅' : '❌',
            tags: tags.map((tag: any) => `${tag.tag_dimension_id}:${tag.tag_name}`)
          });

          // 详细调试标签匹配
          console.log(`🔍 教材 ${index + 1} 详细标签分析:`);
          console.log('  - 查找的学段ID:', filters.stage, '配置的维度ID:', this.config.tagDimensions.stage);
          console.log('  - 查找的年级ID:', filters.grade, '配置的维度ID:', this.config.tagDimensions.grade);
          console.log('  - 查找的学科ID:', filters.subject, '配置的维度ID:', this.config.tagDimensions.subject);

          const stageTag = tags.find((tag: any) => tag.tag_dimension_id === this.config.tagDimensions.stage);
          const gradeTag = tags.find((tag: any) => tag.tag_dimension_id === this.config.tagDimensions.grade);
          const subjectTag = tags.find((tag: any) => tag.tag_dimension_id === this.config.tagDimensions.subject);

          console.log('  - 实际学段标签:', stageTag);
          console.log('  - 实际年级标签:', gradeTag);
          console.log('  - 实际学科标签:', subjectTag);
        }

        return stageMatch && gradeMatch && subjectMatch && versionMatch && volumeMatch;
      });

      console.log('📊 匹配统计:', {
        学段: stageMatches,
        年级: gradeMatches,
        学科: subjectMatches,
        版本: versionMatches,
        册次: volumeMatches,
        部分匹配: partialMatches
      });

      if (matchedMaterial) {
        console.log('✅ 找到匹配的教材:', matchedMaterial.title, 'ID:', matchedMaterial.id);
        return matchedMaterial.id;
      } else {
        console.log('❌ 未找到匹配的教材');



        // 如果没有完全匹配，尝试找到核心匹配（学段+年级+学科）
        console.log('🔍 尝试查找核心匹配的教材（学段+年级+学科）...');
        const coreMatches = materials.filter((material: any) => {
          const tags = material.tag_list || [];
          const stageMatch = !filters.stage || tags.find((tag: any) =>
            tag.tag_dimension_id === this.config.tagDimensions.stage && tag.tag_id === filters.stage);
          const gradeMatch = !filters.grade || tags.find((tag: any) =>
            tag.tag_dimension_id === this.config.tagDimensions.grade && tag.tag_id === filters.grade);
          const subjectMatch = !filters.subject || tags.find((tag: any) => {
            if (tag.tag_dimension_id !== this.config.tagDimensions.subject) return false;

            // 精确匹配优先
            if (tag.tag_id === filters.subject) return true;

            return false;
          });

          return stageMatch && gradeMatch && subjectMatch;
        });

        console.log(`🔍 找到 ${coreMatches.length} 个核心匹配的教材`);

        if (coreMatches.length > 0) {
          // 显示前几个核心匹配的教材
          coreMatches.slice(0, 5).forEach((material: any, index: number) => {
            const tags = material.tag_list || [];
            const versionTag = tags.find((tag: any) => tag.tag_dimension_id === this.config.tagDimensions.version);
            const volumeTag = tags.find((tag: any) => tag.tag_dimension_id === this.config.tagDimensions.volume);

            console.log(`📚 核心匹配 ${index + 1}: ${material.title}`, {
              版本: versionTag?.tag_name || '无',
              册次: volumeTag?.tag_name || '无',
              id: material.id
            });
          });

          // 优先选择纯"语文"学科的教材
          let bestMatch = coreMatches.find((material: any) => {
            const tags = material.tag_list || [];
            const subjectTag = tags.find((tag: any) => tag.tag_dimension_id === this.config.tagDimensions.subject);
            return subjectTag && subjectTag.tag_name === '语文';
          });

          // 如果没有纯"语文"，选择版本和册次都匹配的
          if (!bestMatch) {
            bestMatch = coreMatches.find((material: any) => {
              const tags = material.tag_list || [];
              const versionMatch = !filters.version || tags.find((tag: any) =>
                tag.tag_dimension_id === this.config.tagDimensions.version && tag.tag_id === filters.version);
              const volumeMatch = !filters.volume || tags.find((tag: any) =>
                tag.tag_dimension_id === this.config.tagDimensions.volume && tag.tag_id === filters.volume);
              return versionMatch && volumeMatch;
            });
          }

          // 如果没有完全匹配，选择版本匹配的
          if (!bestMatch) {
            bestMatch = coreMatches.find((material: any) => {
              const tags = material.tag_list || [];
              return tags.find((tag: any) =>
                tag.tag_dimension_id === this.config.tagDimensions.version && tag.tag_id === filters.version);
            });
          }

          // 如果还没有，选择第一个核心匹配的
          if (!bestMatch) {
            bestMatch = coreMatches[0];
          }

          console.log('🔧 选择最佳匹配的教材:', bestMatch.title, 'ID:', bestMatch.id);
          return bestMatch.id;
        }

        return null;
      }
    } catch (error) {
      console.error('查找教材ID时出错:', error);
      return null;
    }
  }

  /**
   * 获取教材的树形结构（单元、章节信息）
   */
  private async getMaterialTree(materialId: string): Promise<any> {
    try {
      const treeAPI = this.config.api.endpoints.materialTree.replace('{materialId}', materialId);
      console.log('🔍 获取教材树形结构:', treeAPI);

      const electronAPI = (window as any).electronAPI;
      if (!electronAPI?.apiRequest) {
        throw new Error('Electron API代理不可用');
      }

      const response = await electronAPI.apiRequest(treeAPI);
      if (!response.success) {
        console.log('⚠️ 获取教材树形结构失败，将使用回退方案');
        return null;
      }

      console.log('✅ 成功获取教材树形结构');
      return response.data;
    } catch (error) {
      console.log('⚠️ 获取教材树形结构异常，将使用回退方案:', error);
      return null;
    }
  }

  /**
   * 从树形结构中构建ID到名称的映射
   */
  private buildChapterMapping(tree: any): Map<string, string> {
    const mapping = new Map<string, string>();

    // 调试：输出树形结构的格式
    console.log('🔍 树形结构调试:', JSON.stringify(tree, null, 2).substring(0, 1000) + '...');

    if (!tree) {
      console.log('⚠️ 树形结构为空');
      return mapping;
    }

    // 递归遍历树形结构
    const traverse = (node: any, parentPath: string = '', depth: number = 0) => {
      if (depth < 3) { // 只在前3层输出调试信息
        console.log(`${'  '.repeat(depth)}节点: id=${node.id}, title=${node.title}, child_nodes=${node.child_nodes?.length || 0}`);
      }

      if (node.id && node.title) {
        const fullPath = parentPath ? `${parentPath}/${node.title}` : node.title;
        mapping.set(node.id, node.title);
        mapping.set(node.id + '_path', fullPath);
      }

      // 使用正确的字段名 child_nodes
      if (node.child_nodes && Array.isArray(node.child_nodes)) {
        node.child_nodes.forEach((child: any) => {
          traverse(child, node.title || parentPath, depth + 1);
        });
      }
    };

    // 处理树形结构（可能是数组）
    if (Array.isArray(tree)) {
      tree.forEach(rootNode => traverse(rootNode));
    } else {
      traverse(tree);
    }

    console.log(`🔍 构建完成，映射条目数量: ${mapping.size}`);
    return mapping;
  }

  /**
   * 从章节名称中提取排序信息
   */
  private extractChapterOrder(chapterName: string): number {
    // 1. 课文编号（如"1 观潮"、"2 走月亮"、"9 古诗三首"）
    const lessonMatch = chapterName.match(/^(\d+)[\s*]*(.+)/);
    if (lessonMatch) {
      return parseInt(lessonMatch[1]) * 10; // 乘以10为其他内容留出空间
    }

    // 2. 古诗子内容（暮江吟、题西林壁、雪梅等）
    // 这些应该归属到"9 古诗三首"下，给予特殊的排序
    const poemNames = ['暮江吟', '题西林壁', '雪梅', '山行', '枫桥夜泊', '出塞'];
    if (poemNames.some(poem => chapterName.includes(poem))) {
      // 根据诗名给予不同的子排序
      if (chapterName.includes('暮江吟')) return 91; // 9 古诗三首的第1首
      if (chapterName.includes('题西林壁')) return 92; // 9 古诗三首的第2首
      if (chapterName.includes('雪梅')) return 93; // 9 古诗三首的第3首
      return 90; // 其他古诗
    }

    // 3. 口语交际
    if (chapterName.includes('口语交际')) {
      return 800; // 排在课文后面
    }

    // 4. 习作
    if (chapterName.includes('习作')) {
      return 850; // 排在口语交际后面
    }

    // 5. 语文园地
    if (chapterName.includes('语文园地')) {
      return 900; // 排在习作后面
    }

    // 6. 快乐读书吧
    if (chapterName.includes('快乐读书吧')) {
      return 950; // 排在语文园地后面
    }

    // 7. 其他特殊内容
    if (chapterName.includes('学习导引')) {
      return 5; // 排在课文前面
    }

    if (chapterName.includes('复习整理')) {
      return 980; // 排在最后
    }

    // 8. 默认排序
    return 999;
  }

  /**
   * 处理古诗资源的层级结构，保持三级显示并补充缺失的子诗
   */
  private processPoetryHierarchy(resources: CourseResource[], chapterMapping: Map<string, string>): CourseResource[] {
    const processedResources: CourseResource[] = [];
    const seen = new Set<string>();

    // 定义古诗子内容名称
    const poemNames = ['暮江吟', '题西林壁', '雪梅', '山行', '枫桥夜泊', '出塞', '凉州词', '夏日绝句'];

    console.log(`🎭 开始处理古诗层级结构，输入资源数量: ${resources.length}`);

    for (const resource of resources) {
      const title = resource.title;
      const chapter = resource.metadata?.chapter || '';

      // 检查是否是古诗子内容
      const isPoetrySubContent = poemNames.some(poem => title.includes(poem));

      // 检查是否是古诗三首主课程
      const isPoetryMainCourse = title.includes('古诗三首');

      if (isPoetrySubContent) {
        // 对于古诗子内容，标记为子项
        const uniqueKey = `${resource.id}-${chapter}-${title}`;

        if (!seen.has(uniqueKey)) {
          seen.add(uniqueKey);

          const processedResource = {
            ...resource,
            metadata: {
              ...resource.metadata,
              isSubItem: true,
              parentChapter: chapter
            }
          };

          processedResources.push(processedResource);
        }
      } else if (isPoetryMainCourse) {
        // 对于古诗三首主课程，先添加主课程，然后根据chapter_ids生成缺失的子诗
        const uniqueKey = `${resource.id}-${title}`;
        if (!seen.has(uniqueKey)) {
          seen.add(uniqueKey);
          processedResources.push(resource);

          // 根据chapter_ids生成缺失的子诗
          this.generateMissingPoems(resource, processedResources, seen, chapterMapping);
        }
      } else {
        // 对于非古诗内容，正常处理
        // 使用更精确的唯一键，避免误删不同的资源
        const uniqueKey = `${resource.id}-${title}`;
        if (!seen.has(uniqueKey)) {
          seen.add(uniqueKey);
          processedResources.push(resource);
        } else {
          console.log(`🔄 跳过重复资源: ${title} (ID: ${resource.id})`);
        }
      }
    }

    console.log(`🎭 古诗层级处理完成，输出资源数量: ${processedResources.length}`);
    return processedResources;
  }

  /**
   * 根据古诗三首资源的chapter_ids生成缺失的子诗
   */
  private generateMissingPoems(poetryResource: CourseResource, processedResources: CourseResource[], seen: Set<string>, chapterMapping: Map<string, string>) {
    if (!poetryResource.metadata?.chapter_ids) return;

    // 遍历chapter_ids，查找子诗
    for (const chapterId of poetryResource.metadata.chapter_ids) {
      const poemName = chapterMapping.get(chapterId);
      if (poemName && ['暮江吟', '题西林壁', '雪梅', '出塞', '凉州词', '夏日绝句'].includes(poemName)) {
        const uniqueKey = `${poetryResource.metadata.chapter}-${poemName}`;

        // 检查是否已经存在这个子诗
        if (!seen.has(uniqueKey)) {
          seen.add(uniqueKey);

          // 创建虚拟的子诗资源
          const virtualPoemResource: CourseResource = {
            id: `virtual-${chapterId}`,
            title: poemName,
            description: `${poetryResource.metadata.chapter}中的${poemName}`,
            type: poetryResource.type,
            url: poetryResource.url,
            downloadUrl: poetryResource.downloadUrl,
            requiresAuth: poetryResource.requiresAuth,
            accessLevel: poetryResource.accessLevel,
            metadata: {
              ...poetryResource.metadata,
              chapter: poetryResource.metadata.chapter,
              isSubItem: true,
              parentChapter: poetryResource.metadata.chapter,
              chapterOrder: this.extractChapterOrder(poemName),
              isVirtual: true // 标记为虚拟资源
            }
          };

          processedResources.push(virtualPoemResource);
          console.log(`🎭 生成虚拟子诗资源: ${poemName} (来自 ${poetryResource.metadata.chapter})`);
        }
      }
    }
  }

  /**
   * 搜索资源
   */
  async searchResources(filters: CourseFilters): Promise<CourseResource[]> {
    try {
      console.log('🔍 开始搜索资源');
      console.log('🔍 筛选条件:', JSON.stringify(filters));

      console.log('🔍 筛选条件:', JSON.stringify(filters));

      // 第一步：优先通过标签组合查找教材ID
      let materialId = await this.findMaterialByTagCombination(filters);

      // 如果标签组合查找失败，使用传统的匹配方法
      if (!materialId) {
        console.log('🔄 标签组合查找失败，尝试传统匹配方法...');
        materialId = await this.findMaterialId(filters);
      }

      if (!materialId) {
        console.log('❌ 无法找到匹配的教材，返回空结果');
        console.log('🔍 筛选条件详情:', JSON.stringify(filters, null, 2));
        return [];
      }

      console.log(`✅ 找到匹配的教材ID: ${materialId}`);

      // 第二步：获取教材的树形结构
      const materialTree = await this.getMaterialTree(materialId);
      const chapterMapping = materialTree ? this.buildChapterMapping(materialTree) : new Map<string, string>();

      // 调试：输出章节映射信息
      console.log('📋 章节映射构建结果:');
      console.log(`  映射条目数量: ${chapterMapping.size}`);
      if (chapterMapping.size > 0) {
        console.log('  前10个映射条目:');
        let count = 0;
        for (const [key, value] of chapterMapping.entries()) {
          if (count < 10) {
            console.log(`    ${key} -> ${value}`);
            count++;
          }
        }
      }

      // 第三步：按照真实网站的流程获取资源数据
      // 3.1 首先获取资源分片信息
      const partsUrl = `${this.config.api.baseURL}${this.config.api.endpoints.materialParts.replace('{materialId}', materialId)}`;
      console.log('🔍 获取资源分片信息:', partsUrl);

      const electronAPI = (window as any).electronAPI;
      if (!electronAPI?.apiRequest) {
        throw new Error('Electron API代理不可用，请确保应用在Electron环境中运行');
      }

      const partsResponse = await electronAPI.apiRequest(partsUrl);
      if (!partsResponse.success) {
        console.log('⚠️ 获取资源分片信息失败，尝试直接获取part_100.json');
      }

      // 3.2 获取具体的资源数据
      const resourcesUrl = `${this.config.api.baseURL}${this.config.api.endpoints.materialResources.replace('{materialId}', materialId)}`;
      console.log('🔍 获取资源数据:', resourcesUrl);

      const response = await electronAPI.apiRequest(resourcesUrl);
      if (!response.success) {
        throw new Error(`获取资源数据失败: ${response.error?.message || '未知错误'}`);
      }

      const resourcesData = response.data || [];
      console.log(`✅ 成功获取资源数据，共 ${resourcesData.length} 个资源`);

      // 如果资源数据为空，输出详细的调试信息
      if (resourcesData.length === 0) {
        console.error('❌ API返回的资源数据为空！');
        console.log('📋 API响应详情:', {
          success: response.success,
          dataType: typeof response.data,
          dataKeys: response.data ? Object.keys(response.data) : 'null',
          fullResponse: response
        });
        return [];
      }

      // 调试前几个资源的结构
      if (resourcesData.length > 0) {
        console.log('📋 前5个资源的详细结构:');
        resourcesData.slice(0, 5).forEach((item: any, index: number) => {
          console.log(`资源 ${index + 1}:`, {
            id: item.id,
            title: item.title,
            type: item.type,
            url: item.url,
            chapter_ids: item.chapter_ids,
            chapter_paths: item.chapter_paths,
            allKeys: Object.keys(item),
            hasRequiredFields: {
              id: !!item.id,
              title: !!item.title,
              chapter_ids: Array.isArray(item.chapter_ids),
              chapter_paths: Array.isArray(item.chapter_paths)
            }
          });
        });
      }

      // 获取筛选条件名称用于metadata
      const tagsData = await this.getTagsData();
      const filterNames = await this.getFilterNames(filters, tagsData);

      // 调试：输出前几个资源的详细结构信息
      console.log('📋 API返回的原始数据结构分析:');
      resourcesData.slice(0, 5).forEach((item: any, index: number) => {
        const structureInfo = {
          title: item.title,
          chapter_ids: item.chapter_ids,
          chapter_paths: item.chapter_paths,
          // 显示所有可能的单元相关字段
          allFields: Object.keys(item).filter(key =>
            key.toLowerCase().includes('unit') ||
            key.toLowerCase().includes('chapter') ||
            key.toLowerCase().includes('section') ||
            key.toLowerCase().includes('lesson') ||
            key.toLowerCase().includes('part')
          )
        };
        console.log(`资源 ${index + 1}:`, JSON.stringify(structureInfo, null, 2));
      });

      // 特别调试：查找第三单元古诗相关的资源
      console.log('🔍 第三单元古诗相关资源分析:');
      const poetryResources = resourcesData.filter((item: any) =>
        item.title && (
          item.title.includes('古诗') ||
          item.title.includes('暮江吟') ||
          item.title.includes('题西林壁') ||
          item.title.includes('雪梅')
        )
      );
      console.log(`🔍 找到 ${poetryResources.length} 个古诗相关资源`);
      poetryResources.forEach((item: any, index: number) => {
        console.log(`古诗资源 ${index + 1}: ${item.title}`);
        console.log(`  chapter_ids:`, item.chapter_ids);
        console.log(`  映射结果:`, item.chapter_ids?.map((id: string) => `${id} -> ${chapterMapping.get(id)}`));
      });

      // 特别检查：查找所有包含"雪梅"的资源
      const xuemeiResources = resourcesData.filter((item: any) =>
        item.title && item.title.includes('雪梅')
      );
      console.log(`🔍 雪梅资源检查: 找到 ${xuemeiResources.length} 个包含"雪梅"的资源`);
      xuemeiResources.forEach((item: any, index: number) => {
        console.log(`雪梅资源 ${index + 1}:`, {
          title: item.title,
          chapter_ids: item.chapter_ids,
          id: item.id
        });
      });

      // 转换为标准格式，使用章节映射提取单元信息和排序
      console.log(`🔄 开始转换 ${resourcesData.length} 个原始资源为标准格式`);
      const resources: CourseResource[] = resourcesData.map((item: any, index: number) => {
        // 从chapter_ids中提取单元信息和排序
        let extractedUnit = '';
        let extractedChapter = '';
        let chapterOrder = 999; // 默认排序

        if (item.chapter_ids && Array.isArray(item.chapter_ids) && item.chapter_ids.length > 0) {
          // 查找单元ID（第一个包含"单元"的ID）
          let unitId = '';
          let unitName = '';

          for (const id of item.chapter_ids) {
            const name = chapterMapping.get(id);
            if (name && name.includes('单元')) {
              unitId = id;
              unitName = name;
              extractedUnit = unitName;
              break;
            }
          }

          // 如果没有找到单元，尝试从路径中提取
          if (!extractedUnit && item.chapter_ids.length > 0) {
            const firstId = item.chapter_ids[0];
            const fullPath = chapterMapping.get(firstId + '_path');
            if (fullPath) {
              const unitMatch = fullPath.match(/第[一二三四五六七八九十\d]+单元/);
              if (unitMatch) {
                extractedUnit = unitMatch[0];
              }
            }
          }

          // 提取章节信息和排序
          if (item.chapter_ids.length > 0) {
            let targetId = '';

            if (item.chapter_ids.length >= 3) {
              // 如果有3层或更多，使用倒数第二个（主课程层级）
              targetId = item.chapter_ids[item.chapter_ids.length - 2];
            } else if (item.chapter_ids.length === 2) {
              // 如果只有2层，使用最后一个
              targetId = item.chapter_ids[item.chapter_ids.length - 1];
            } else if (item.chapter_ids.length === 1) {
              // 如果只有1层，检查是否是单元级别的内容
              const singleChapterName = chapterMapping.get(item.chapter_ids[0]);
              if (singleChapterName && !singleChapterName.includes('单元')) {
                targetId = item.chapter_ids[0];
              }
            }

            if (targetId) {
              const finalChapterName = chapterMapping.get(targetId);
              if (finalChapterName) {
                extractedChapter = finalChapterName;
                chapterOrder = this.extractChapterOrder(finalChapterName);
              }
            }
          }
        }

        return {
          id: item.id || '',
          title: item.title || '',
          description: item.description || '',
          url: item.url || '',
          type: 'video' as const,
          requiresAuth: false,
          accessLevel: 'public' as const,
          metadata: {
            stage: filterNames.stage || '',
            grade: filterNames.grade || '',
            subject: filterNames.subject || '',
            version: filterNames.version || '',
            volume: filterNames.volume || '',
            // 从章节映射中提取的单元信息
            unit: extractedUnit,
            chapter: extractedChapter,
            section: item.section || '',
            lesson: item.lesson || '',
            // 添加章节排序信息
            chapterOrder: chapterOrder,
            // 保留原始的chapter_paths和chapter_ids用于调试
            chapter_paths: item.chapter_paths,
            chapter_ids: item.chapter_ids,
            duration: item.duration || 0,
            fileSize: item.fileSize || 0
          }
        };
      });

      console.log(`✅ 资源转换完成，转换了 ${resources.length} 个资源`);

      // 验证转换结果
      const validResources = resources.filter(r => r.id && r.title);
      if (validResources.length !== resources.length) {
        console.warn(`⚠️ 发现 ${resources.length - validResources.length} 个无效资源（缺少ID或标题）`);
      }

      // 处理古诗层级结构，保持三级显示
      // 临时禁用古诗处理逻辑进行调试
      const ENABLE_POETRY_PROCESSING = false;
      const processedResources = ENABLE_POETRY_PROCESSING
        ? this.processPoetryHierarchy(resources, chapterMapping)
        : resources;

      console.log(`🎯 处理前: ${resources.length} 个课程资源`);
      console.log(`🎯 处理后: ${processedResources.length} 个课程资源`);

      // 如果资源数量大幅减少，输出详细信息
      if (resources.length > 0 && processedResources.length === 0) {
        console.error('❌ 所有资源都被过滤掉了！');
        console.log('原始资源标题列表:');
        resources.forEach((r, i) => console.log(`  ${i + 1}. ${r.title}`));
      } else if (processedResources.length < resources.length * 0.5) {
        console.warn(`⚠️ 资源数量减少了 ${resources.length - processedResources.length} 个`);
        const filteredTitles = resources.map(r => r.title).filter(title =>
          !processedResources.some(pr => pr.title === title)
        );
        console.log('被过滤的资源标题:', filteredTitles);
      }

      return processedResources;


    } catch (error) {
      console.error('搜索资源失败:', error);
      throw new ApiError('搜索资源失败', { originalError: error as Error });
    }
  }

  /**
   * 处理资源权限信息
   */
  private async processResourcesWithPermissions(resources: CourseResource[]): Promise<CourseResource[]> {
    const isAuthenticated = await this.checkAuthStatus();
    
    return resources.map(resource => ({
      ...resource,
      // 根据认证状态和资源类型确定访问权限
      requiresAuth: this.determineAuthRequirement(resource),
      accessLevel: this.determineAccessLevel(resource, isAuthenticated)
    }));
  }

  /**
   * 确定资源是否需要认证
   */
  private determineAuthRequirement(resource: CourseResource): boolean {
    // 视频资源通常需要认证
    if (resource.type === 'video') {
      return true;
    }
    
    // 某些高级教材可能需要认证
    if (resource.metadata.stage === 'high' || resource.metadata.subject === 'advanced') {
      return true;
    }
    
    return resource.requiresAuth || false;
  }

  /**
   * 确定资源访问级别
   */
  private determineAccessLevel(resource: CourseResource, isAuthenticated: boolean): 'public' | 'registered' | 'premium' {
    if (!this.determineAuthRequirement(resource)) {
      return 'public';
    }
    
    if (isAuthenticated) {
      // 已认证用户可以访问注册级别的资源
      return resource.accessLevel === 'premium' ? 'premium' : 'registered';
    }
    
    return resource.accessLevel || 'registered';
  }

  /**
   * 获取资源详情
   */
  async getResourceDetail(resourceId: string): Promise<ResourceDetail> {
    try {
      const response = await this.get<ResourceDetail>(`/api/resource/${resourceId}`);
      
      if (!response.success || !response.data) {
        throw new ApiError('获取资源详情失败', { context: { resourceId } });
      }

      // 检查用户权限并处理资源详情
      const processedDetail = await this.processResourceDetailWithPermissions(response.data);
      return processedDetail;
    } catch (error) {
      throw new ApiError('获取资源详情失败', { originalError: error as Error });
    }
  }

  /**
   * 处理资源详情的权限信息
   */
  private async processResourceDetailWithPermissions(detail: ResourceDetail): Promise<ResourceDetail> {
    const isAuthenticated = await this.checkAuthStatus();
    
    return {
      ...detail,
      requiresAuth: this.determineAuthRequirement(detail),
      accessLevel: this.determineAccessLevel(detail, isAuthenticated)
    };
  }

  /**
   * 检查用户对特定资源的访问权限
   */
  async checkResourceAccess(resourceId: string): Promise<{
    hasAccess: boolean;
    requiresAuth: boolean;
    accessLevel: 'public' | 'registered' | 'premium';
    message?: string;
  }> {
    try {
      const isAuthenticated = await this.checkAuthStatus();
      const response = await this.get<{
        hasAccess: boolean;
        requiresAuth: boolean;
        accessLevel: string;
        message?: string;
      }>(`/api/resource/${resourceId}/access`);
      
      if (!response.success || !response.data) {
        // 如果API不可用，使用本地逻辑判断
        return {
          hasAccess: !isAuthenticated ? false : true,
          requiresAuth: true,
          accessLevel: isAuthenticated ? 'registered' : 'public',
          message: isAuthenticated ? undefined : '需要登录才能访问此资源'
        };
      }

      return {
        ...response.data,
        accessLevel: response.data.accessLevel as 'public' | 'registered' | 'premium'
      };
    } catch (error) {
      // 发生错误时的默认处理
      const isAuthenticated = await this.checkAuthStatus();
      return {
        hasAccess: isAuthenticated,
        requiresAuth: true,
        accessLevel: isAuthenticated ? 'registered' : 'public',
        message: '无法验证资源访问权限'
      };
    }
  }

  /**
   * 获取视频播放列表
   */
  async getVideoPlaylist(videoId: string): Promise<M3U8Playlist> {
    try {
      const response = await this.get<M3U8Playlist>(`/api/video/${videoId}/playlist`);
      
      if (!response.success || !response.data) {
        throw new ApiError('获取视频播放列表失败', { context: { videoId } });
      }

      return response.data;
    } catch (error) {
      throw new ApiError('获取视频播放列表失败', { originalError: error as Error });
    }
  }

  /**
   * 获取验证码
   */
  async getCaptcha(): Promise<CaptchaInfo> {
    try {
      const response = await this.get<CaptchaInfo>('/api/auth/captcha');
      
      if (!response.success || !response.data) {
        throw new ApiError('获取验证码失败');
      }

      return response.data;
    } catch (error) {
      throw new ApiError('获取验证码失败', { originalError: error as Error });
    }
  }

  /**
   * 获取请求统计信息
   */
  getRequestStats(): {
    totalRequests: number;
    requestsInLastMinute: number;
    currentUserAgent: string;
    lastRequestTime: Date | null;
  } {
    const now = Date.now();
    const requestsInLastMinute = this.requestTimes.filter(time => now - time < 60000).length;
    
    return {
      totalRequests: this.requestCount,
      requestsInLastMinute,
      currentUserAgent: this.userAgents[this.currentUserAgentIndex],
      lastRequestTime: this.lastRequestTime > 0 ? new Date(this.lastRequestTime) : null
    };
  }

  /**
   * 重置请求统计
   */
  resetRequestStats(): void {
    this.requestCount = 0;
    this.requestTimes = [];
    this.lastRequestTime = 0;
  }

  /**
   * 设置自定义User-Agent列表
   */
  setUserAgents(userAgents: string[]): void {
    if (userAgents.length === 0) {
      throw new Error('User-Agent列表不能为空');
    }
    this.userAgents = [...userAgents];
    this.currentUserAgentIndex = 0;
  }

  /**
   * 获取当前配置
   */
  getConfig(): {
    baseURL: string;
    timeout: number;
    minRequestInterval: number;
    maxRequestsPerMinute: number;
    userAgentCount: number;
  } {
    return {
      baseURL: this.axiosInstance.defaults.baseURL || '',
      timeout: this.axiosInstance.defaults.timeout || 0,
      minRequestInterval: this.minRequestInterval,
      maxRequestsPerMinute: this.maxRequestsPerMinute,
      userAgentCount: this.userAgents.length
    };
  }
}

export default SmartEduClient;